import React, { useState } from "react";
import { MemberWrapper } from "../../../components/MemberWrapper";
import { InteractiveButton } from "../../../components/InteractiveButton";
import { Link, useNavigate } from "react-router-dom";
import { MkdLoader } from "../../../components/MkdLoader";
import { CustomPaginationBar } from "../../../components/PaginationBar";
import { MkdInputV2 } from "../../../components/MkdInputV2";
import { Skeleton } from "../../../components/Skeleton";
import { PlusIcon, EditIcon, TrashIcon } from "../../../assets/svgs";
import ProvideShippingInfoModal from "../../../components/ProvideShippingInfoModal";
import OffersReceivedModal from "../../../components/OffersReceivedModal";
import DeleteListingModal from "../../../components/DeleteListingModal/DeleteListingModal";
import PromoteListingModal from "../../../components/PromoteListingModal/PromoteListingModal";
import {
  useMemberListingsQuery,
  useListingOffersQuery,
  useAcceptOfferMutation,
  useRejectOfferMutation,
  IListing,
  IListingFilters,
} from "../../../query/useListings";

const MemberListingsListPage = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<"all" | "sold">("all");
  const [showFilters, setShowFilters] = useState(false);
  const [showSort, setShowSort] = useState(false);
  const [filters, setFilters] = useState<IListingFilters>({
    page: 1,
    limit: 6,
    search: "",
    tab: "all",
    status: "All Statuses",
    type: "All",
    category: "All Categories",
    dateAdded: "Any Time",
    sortBy: "Newest Sold",
    minPrice: undefined,
    maxPrice: undefined,
  });
  const [priceRange, setPriceRange] = useState({ min: "", max: "" });
  const [searchInput, setSearchInput] = useState("");
  const [isShippingModalOpen, setIsShippingModalOpen] = useState(false);
  const [selectedListingId, setSelectedListingId] = useState<number | null>(
    null
  );
  const [isOffersModalOpen, setIsOffersModalOpen] = useState(false);
  const [selectedListingName, setSelectedListingName] = useState("");
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isPromoteModalOpen, setIsPromoteModalOpen] = useState(false);

  // Query hooks
  const {
    data: listingsData,
    isLoading,
    error,
    refetch,
  } = useMemberListingsQuery(filters);

  const { data: offersData, isLoading: offersLoading } = useListingOffersQuery(
    selectedListingId || 0,
    { page: 1, limit: 20 }
  );

  const { mutate: acceptOffer, isPending: isAccepting } =
    useAcceptOfferMutation();
  const { mutate: rejectOffer, isPending: isRejecting } =
    useRejectOfferMutation();

  // Get data from query
  const listings = listingsData?.data || [];
  const pagination = (listingsData as any)?.data?.pagination || {
    page: 1,
    totalPages: 5,
    total: 24,
    limit: 6,
  };

  // Debug logging
  console.log("listingsData:", listingsData);
  console.log("pagination:", pagination);

  // Filter handlers
  const handleTabChange = (tab: "all" | "sold") => {
    setActiveTab(tab);
    setFilters((prev) => ({
      ...prev,
      tab: tab,
      page: 1,
    }));
  };

  const handleSearch = () => {
    setFilters((prev) => ({
      ...prev,
      search: searchInput,
      page: 1,
    }));
  };

  const handleFilterChange = (key: string, value: any) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
      page: 1,
    }));
  };

  const handlePageChange = (page: number) => {
    setFilters((prev) => ({ ...prev, page }));
  };

  const handleProvideShippingInfo = (listingId: number) => {
    setSelectedListingId(listingId);
    setIsShippingModalOpen(true);
  };

  const handleViewOffers = (listingId: number, listingName: string) => {
    setSelectedListingId(listingId);
    setSelectedListingName(listingName);
    setIsOffersModalOpen(true);
  };

  const handleCloseOffersModal = () => {
    setIsOffersModalOpen(false);
    setSelectedListingId(null);
    setSelectedListingName("");
  };

  const handleAcceptOffer = (offerId: number) => {
    acceptOffer(offerId, {
      onSuccess: () => {
        // Refetch listings to update offer counts
        refetch();
      },
    });
  };

  const handleRejectOffer = (offerId: number) => {
    rejectOffer(offerId, {
      onSuccess: () => {
        // Refetch listings to update offer counts
        refetch();
      },
    });
  };

  const handlePromoteListing = (listingId: number) => {
    setSelectedListingId(listingId);
    setIsPromoteModalOpen(true);
  };

  const confirmPromoteListing = (days: number) => {
    if (selectedListingId) {
      console.log(`Promoting listing ${selectedListingId} for ${days} days`);
      setIsPromoteModalOpen(false);
      setSelectedListingId(null);
      // Refetch listings to show updated status
      refetch();
    }
  };

  const handleDeleteListing = (listingId: number, listingName: string) => {
    setSelectedListingId(listingId);
    setSelectedListingName(listingName);
    setIsDeleteModalOpen(true);
  };

  const confirmDeleteListing = () => {
    if (selectedListingId) {
      console.log("Deleting listing:", selectedListingId);
      setIsDeleteModalOpen(false);
      setSelectedListingId(null);
      setSelectedListingName("");
      // Refetch listings to show updated list
      refetch();
    }
  };

  const handleShippingInfoSubmit = (shippingInfo: {
    carrierName: string;
    trackingNumber: string;
    shippingDate: string;
    proofFile?: File;
  }) => {
    // Here you would typically send the shipping info to your backend
    console.log(
      "Shipping info submitted:",
      shippingInfo,
      "for listing:",
      selectedListingId
    );
    // You can add API call here to update the listing with shipping info
    setIsShippingModalOpen(false);
    setSelectedListingId(null);
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-500 text-white";
      case "expired":
        return "bg-red-500 text-white";
      case "sponsored":
        return "bg-yellow-500 text-black";
      case "draft":
        return "bg-gray-500 text-white";
      case "pending_shipping":
        return "bg-orange-500 text-white";
      case "in_transit":
        return "bg-blue-500 text-white";
      case "shipped":
        return "bg-green-500 text-white";
      case "booked":
        return "bg-purple-500 text-white";
      case "waiting_confirmation":
        return "bg-yellow-500 text-white";
      case "completed":
        return "bg-green-600 text-white";
      default:
        return "bg-gray-500 text-white";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "active":
        return "Active";
      case "expired":
        return "Expired 2 days ago";
      case "sponsored":
        return "Sponsored";
      case "draft":
        return "Draft";
      case "pending_shipping":
        return "Pending Shipping";
      case "in_transit":
        return "In Transit";
      case "shipped":
        return "Shipped";
      case "booked":
        return "Booked";
      case "waiting_confirmation":
        return "Waiting Confirmation";
      case "completed":
        return "Completed";
      default:
        return status;
    }
  };

  return (
    <MemberWrapper>
      <div className="p-6 min-h-screen bg-[#0F2C59]">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-white">My Listings</h1>
          <Link
            to="/member/listings/add"
            className="bg-[#E63946] text-white px-4 py-2 rounded-md hover:bg-[#d32f2f] flex items-center gap-2 text-sm font-medium"
          >
            + Add New Listing
          </Link>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="flex space-x-0">
            <button
              onClick={() => handleTabChange("all")}
              className={`px-6 py-3 text-sm font-medium rounded-l-md border ${
                activeTab === "all"
                  ? "bg-[#E63946] text-white border-[#E63946]"
                  : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
              }`}
            >
              All Listings
            </button>
            <button
              onClick={() => handleTabChange("sold")}
              className={`px-6 py-3 text-sm font-medium rounded-r-md border-t border-r border-b ${
                activeTab === "sold"
                  ? "bg-[#0F2C59] text-white border-[#0F2C59]"
                  : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
              }`}
            >
              Sold & Action Needed
            </button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="mb-6">
          <div className="flex gap-4 mb-4">
            <div className="flex-1">
              <div className="relative">
                <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                  <svg
                    className="w-4 h-4 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="Search by item name, category, or tag"
                  value={searchInput}
                  onChange={(e) => setSearchInput(e.target.value)}
                  onKeyPress={(e) => e.key === "Enter" && handleSearch()}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                />
              </div>
            </div>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="px-4 py-2 border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 flex items-center gap-2 text-sm font-medium"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.121A1 1 0 013 6.414V4z"
                />
              </svg>
              Filters
            </button>
            <button className="px-4 py-2 border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 flex items-center gap-2 text-sm font-medium">
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"
                />
              </svg>
              Sort
            </button>
          </div>

          {/* Filter Dropdowns */}
          {showFilters && (
            <>
              <div className="grid grid-cols-4 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Status
                  </label>
                  <select
                    value={filters.status}
                    onChange={(e) =>
                      handleFilterChange("status", e.target.value)
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                  >
                    <option>All Statuses</option>
                    {activeTab === "sold" ? (
                      <>
                        <option>Booked</option>
                        <option>Waiting Confirmation</option>
                        <option>Completed</option>
                      </>
                    ) : (
                      <>
                        <option>Active</option>
                        <option>Draft</option>
                        <option>Expired</option>
                        <option>Sponsored</option>
                      </>
                    )}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Type
                  </label>
                  <select
                    value={filters.type}
                    onChange={(e) => handleFilterChange("type", e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                  >
                    <option>All</option>
                    <option>Item</option>
                    <option>Service</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Category
                  </label>
                  <select
                    value={filters.category}
                    onChange={(e) =>
                      handleFilterChange("category", e.target.value)
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                  >
                    <option>All Categories</option>
                    <option>Electronics</option>
                    <option>Sports</option>
                    <option>Services</option>
                    <option>Furniture</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {activeTab === "sold" ? "Sort By" : "Date Added"}
                  </label>
                  <select
                    value={
                      activeTab === "sold"
                        ? filters.sortBy || "Newest Sold"
                        : filters.dateAdded
                    }
                    onChange={(e) =>
                      handleFilterChange(
                        activeTab === "sold" ? "sortBy" : "dateAdded",
                        e.target.value
                      )
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                  >
                    {activeTab === "sold" ? (
                      <>
                        <option>Newest Sold</option>
                        <option>Oldest Sold</option>
                        <option>Price High to Low</option>
                        <option>Price Low to High</option>
                      </>
                    ) : (
                      <>
                        <option>Any Time</option>
                        <option>Last 7 days</option>
                        <option>Last 30 days</option>
                        <option>Last 3 months</option>
                      </>
                    )}
                  </select>
                </div>
              </div>

              {/* Price Range Row */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Price Range (eBa$)
                  </label>
                  <div className="flex gap-2">
                    <input
                      type="text"
                      placeholder="Min"
                      value={priceRange.min}
                      onChange={(e) => {
                        const newPriceRange = {
                          ...priceRange,
                          min: e.target.value,
                        };
                        setPriceRange(newPriceRange);
                        setFilters((prev) => ({
                          ...prev,
                          minPrice: e.target.value
                            ? parseFloat(e.target.value)
                            : undefined,
                          page: 1,
                        }));
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                    />
                    <input
                      type="text"
                      placeholder="Max"
                      value={priceRange.max}
                      onChange={(e) => {
                        const newPriceRange = {
                          ...priceRange,
                          max: e.target.value,
                        };
                        setPriceRange(newPriceRange);
                        setFilters((prev) => ({
                          ...prev,
                          maxPrice: e.target.value
                            ? parseFloat(e.target.value)
                            : undefined,
                          page: 1,
                        }));
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                    />
                  </div>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Listings Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-4">
                <Skeleton className="h-48 w-full mb-4 rounded" />
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-2/3 mb-4" />
                <div className="flex justify-between items-center mb-4">
                  <Skeleton className="h-6 w-20" />
                  <Skeleton className="h-4 w-16" />
                </div>
                <div className="flex gap-2">
                  <Skeleton className="h-8 w-20" />
                  <Skeleton className="h-8 w-20" />
                  <Skeleton className="h-8 w-20" />
                </div>
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <p className="text-gray-500 mb-4">Failed to load listings</p>
            <button
              onClick={() => refetch()}
              className="bg-[#E63946] text-white px-4 py-2 rounded-md hover:bg-red-700"
            >
              Retry
            </button>
          </div>
        ) : listings.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500 mb-4">No listings found</p>
            <Link
              to="/member/add-listing"
              className="bg-[#E63946] text-white px-4 py-2 rounded-md hover:bg-red-700"
            >
              Create Your First Listing
            </Link>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
              {listings.map((listing: any) => (
                <div
                  key={listing.id}
                  className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
                >
                  {/* Image Section */}
                  <div className="relative">
                    <img
                      src={listing.image || "/api/placeholder/300/200"}
                      alt={listing.name}
                      className="w-full h-48 object-cover"
                    />

                    {/* Status Badges - Top Left */}
                    <div className="absolute top-3 left-3 flex flex-col gap-1">
                      {/* Offers badge */}
                      {listing.pendingOffers > 0 && (
                        <button
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleViewOffers(listing.id, listing.name);
                          }}
                          className="bg-blue-500 text-white px-2 py-1 rounded text-xs font-medium hover:bg-blue-600 transition-colors cursor-pointer flex items-center gap-1"
                        >
                          💰 {listing.pendingOffers} Offer
                          {listing.pendingOffers !== 1 ? "s" : ""}
                        </button>
                      )}
                      {/* Type badge */}
                      <span className="text-xs font-medium text-gray-600 bg-white px-2 py-1 rounded">
                        {listing.type}
                      </span>
                    </div>

                    {/* Status Badge - Top Right */}
                    <div className="absolute top-3 right-3">
                      <span
                        className={`text-xs font-medium px-2 py-1 rounded ${getStatusBadgeColor(listing.status)}`}
                      >
                        {getStatusText(listing.status)}
                      </span>
                    </div>
                  </div>

                  {/* Card Content */}
                  <div className="p-4">
                    {/* Title */}
                    <h3 className="font-bold text-[#0F2C59] mb-2 text-lg">
                      {listing.name}
                    </h3>

                    {/* Price */}
                    <p className="text-[#E63946] font-bold text-xl mb-2">
                      eBa$ {listing.price}
                      {listing.type === "Service" ? "/hr" : ""}
                    </p>

                    {/* Description */}
                    <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                      {listing.description}
                    </p>

                    {/* Category Tag */}
                    <div className="mb-3">
                      <span className="inline-block bg-gray-100 text-gray-700 px-3 py-1 rounded text-xs font-medium">
                        {listing.category}
                      </span>
                    </div>

                    {/* Quantity/Availability */}
                    {activeTab === "sold" ? (
                      <div className="mb-3">
                        <span className="inline-block bg-blue-100 text-blue-700 px-3 py-1 rounded text-xs font-medium">
                          📅 {listing.sold_date}
                        </span>
                      </div>
                    ) : (
                      <div className="mb-3">
                        <span className="inline-block bg-blue-100 text-blue-700 px-3 py-1 rounded text-xs font-medium">
                          Quantity: {listing.quantity} available |{" "}
                          {listing.soldCount || 0} sold
                        </span>
                      </div>
                    )}

                    {/* Stats */}
                    <div className="text-xs text-gray-500 mb-4">
                      {activeTab === "sold" ? (
                        <div className="flex items-center gap-1">
                          <span>👤 Client: {listing.buyer || "N/A"}</span>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <span>{listing.viewCount || 0} views</span>
                          <span>•</span>
                          <span>{listing.inquiryCount || 0} inquiries</span>
                          <span>•</span>
                          <span>
                            Added {listing.sold_date || "Apr 10, '25"}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Action Buttons Row */}
                    <div className="flex gap-2 mb-3">
                      <button
                        onClick={() =>
                          navigate(`/member/listings/edit/${listing.id}`)
                        }
                        className="flex items-center gap-1 text-xs text-gray-600 hover:text-gray-800 border border-gray-300 px-2 py-1 rounded"
                      >
                        ✏️ Edit
                      </button>
                      <button
                        onClick={() =>
                          handleDeleteListing(listing.id, listing.name)
                        }
                        className="flex items-center gap-1 text-xs text-gray-600 hover:text-gray-800 border border-gray-300 px-2 py-1 rounded"
                      >
                        🗑️ Delete
                      </button>
                      <button
                        onClick={() =>
                          navigate(`/member/listings/view/${listing.id}`)
                        }
                        className="flex items-center gap-1 text-xs text-gray-600 hover:text-gray-800 border border-gray-300 px-2 py-1 rounded"
                      >
                        👁️ View
                      </button>
                    </div>

                    {/* Primary Action Button */}
                    {activeTab === "sold" ? (
                      <>
                        {listing.status === "completed" && (
                          <button className="w-full bg-green-600 text-white text-sm py-2 px-3 rounded hover:bg-green-700 flex items-center justify-center gap-1">
                            ✓ Mark as Completed
                          </button>
                        )}
                        {listing.status === "waiting_confirmation" && (
                          <div className="w-full text-center text-xs text-yellow-600 py-2">
                            ⏳ Waiting for buyer confirmation...
                          </div>
                        )}
                        {listing.status === "booked" && (
                          <>
                            <button
                              onClick={() =>
                                handleProvideShippingInfo(listing.id)
                              }
                              className="w-full bg-[#0F2C59] text-white text-sm py-2 px-3 rounded hover:bg-blue-800 flex items-center justify-center gap-1 mb-2"
                            >
                              📦 Provide Shipping Info
                            </button>
                            <button className="w-full bg-blue-600 text-white text-sm py-2 px-3 rounded hover:bg-blue-700 flex items-center justify-center gap-1">
                              📅 View Details
                            </button>
                          </>
                        )}
                      </>
                    ) : (
                      <>
                        {listing.status === "active" && (
                          <button
                            onClick={() => handlePromoteListing(listing.id)}
                            className="w-full bg-[#E63946] text-white text-sm py-2 px-3 rounded hover:bg-[#d32f2f] flex items-center justify-center gap-1"
                          >
                            📢 Promote
                          </button>
                        )}

                        {listing.status === "draft" && (
                          <button className="w-full bg-[#E63946] text-white text-sm py-2 px-3 rounded hover:bg-[#d32f2f] flex items-center justify-center gap-1">
                            📤 Publish
                          </button>
                        )}

                        {listing.status === "expired" && (
                          <button className="w-full bg-gray-500 text-white text-sm py-2 px-3 rounded hover:bg-gray-600 flex items-center justify-center gap-1">
                            🔄 Reactivate
                          </button>
                        )}

                        {listing.status === "sponsored" && (
                          <div className="w-full text-center text-xs text-yellow-600 py-2">
                            ⭐ Sponsored Listing
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {listings.length === 0 && (
              <div className="text-center py-12">
                <div className="text-gray-400 text-lg mb-2">
                  No listings found
                </div>
                <p className="text-gray-500 mb-4">
                  Create your first listing to get started
                </p>
                <Link
                  to="/member/listings/add"
                  className="bg-[#E63946] text-white px-4 py-2 rounded-md hover:bg-[#d32f2f]"
                >
                  Add New Listing
                </Link>
              </div>
            )}
          </>
        )}

        {/* Pagination */}
        <div className="mt-8 bg-[#0F2C59] rounded-lg p-4 flex justify-between items-center">
          {/* Left side - Showing info */}
          <div className="text-gray-300 text-sm">
            Showing {(pagination.page - 1) * (pagination.limit || 6) + 1} of{" "}
            {pagination.total || 0} listings
          </div>

          {/* Right side - Pagination controls */}
          <div className="flex items-center space-x-2">
            {/* Previous button */}
            <button
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page <= 1}
              className="text-gray-300 hover:text-white disabled:text-gray-500 disabled:cursor-not-allowed px-2 py-1"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>

            {/* Page numbers */}
            {Array.from(
              { length: Math.min(5, pagination.totalPages || 1) },
              (_, i) => {
                const pageNum = i + 1;
                const isActive = pageNum === pagination.page;

                return (
                  <button
                    key={pageNum}
                    onClick={() => handlePageChange(pageNum)}
                    className={`px-3 py-1 rounded text-sm font-medium ${
                      isActive
                        ? "bg-[#E63946] text-white"
                        : "text-gray-300 hover:text-white"
                    }`}
                  >
                    {pageNum}
                  </button>
                );
              }
            )}

            {/* Next button */}
            <button
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page >= (pagination.totalPages || 1)}
              className="text-gray-300 hover:text-white disabled:text-gray-500 disabled:cursor-not-allowed px-2 py-1"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Currency Converter Widget */}
        {/* <div className="fixed bottom-4 left-4 bg-white rounded-lg shadow-lg p-4 w-64 border border-gray-200">
          <h4 className="text-sm font-semibold text-gray-900 mb-3">
            Currency Converter
          </h4>
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <input
                type="number"
                placeholder="50"
                defaultValue="50"
                className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm"
              />
              <span className="text-sm text-gray-600">eBa$</span>
            </div>
            <div className="flex items-center space-x-2">
              <select className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm">
                <option>USD</option>
                <option>CAD</option>
                <option>JMD</option>
              </select>
              <span className="text-sm text-gray-600">= 72.50 USD</span>
            </div>
            <p className="text-xs text-gray-500">eBa$50 = 72.50 USD</p>
          </div>
        </div> */}

        {/* Inbox Widget */}
        <div className="fixed bottom-4 right-4 bg-white rounded-lg shadow-lg p-3 border border-gray-200">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
              <span className="text-xs">📧</span>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900">Inbox 3</p>
              <div className="flex items-center space-x-1">
                <div className="w-6 h-6 rounded-full overflow-hidden">
                  <img
                    src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
                    alt="Profile"
                    className="w-full h-full object-cover"
                  />
                </div>
                <span className="text-xs text-gray-600">Alex Johnson</span>
                <button className="text-xs text-gray-400">⌄</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Provide Shipping Info Modal */}
      <ProvideShippingInfoModal
        isOpen={isShippingModalOpen}
        onClose={() => setIsShippingModalOpen(false)}
        onSubmit={handleShippingInfoSubmit}
      />

      {/* Offers Received Modal */}
      <OffersReceivedModal
        isOpen={isOffersModalOpen}
        onClose={handleCloseOffersModal}
        itemName={selectedListingName}
        offers={offersData?.data || []}
        onAcceptOffer={handleAcceptOffer}
        onRejectOffer={handleRejectOffer}
        isLoading={offersLoading}
        isAccepting={isAccepting}
        isRejecting={isRejecting}
      />

      {/* Delete Listing Modal */}
      <DeleteListingModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={confirmDeleteListing}
        listingName={selectedListingName}
      />

      {/* Promote Listing Modal */}
      <PromoteListingModal
        isOpen={isPromoteModalOpen}
        onClose={() => setIsPromoteModalOpen(false)}
        onConfirm={confirmPromoteListing}
      />
    </MemberWrapper>
  );
};

export default MemberListingsListPage;
