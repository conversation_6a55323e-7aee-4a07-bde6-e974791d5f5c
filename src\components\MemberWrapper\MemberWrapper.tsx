import React, { Suspense, memo } from "react";
import { TopHeader } from "@/components/TopHeader";
import { Spinner } from "@/assets/svgs";
import { LazyLoad } from "@/components/LazyLoad";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import MemberHeader from "../MemberHeader";

interface MemberWrapperProps {
  children: React.ReactNode;
}

const MemberWrapper = ({ children }: MemberWrapperProps) => {
  const { state } = useTheme();
  const mode = state?.theme;

  return (
    <>
      <LazyLoad>
        <div
          className={`relative flex h-full max-h-full min-h-full w-full max-w-full overflow-hidden`}
        >
          <MemberHeader />
          <div
            className={`grid h-full max-h-full min-h-full w-full grow grid-rows-[auto_1fr] overflow-x-hidden bg-[#0F2C59]`}
          >
            {/* <TopHeader /> */}
            <Suspense
              fallback={
                <div
                  className={`flex h-full max-h-full min-h-full w-full items-center justify-center`}
                >
                  <Spinner size={40} color={THEME_COLORS[mode].PRIMARY} />
                </div>
              }
            >
              <div className="h-full max-h-full min-h-full w-full overflow-y-auto overflow-x-hidden bg-[#0F2C59]">
                {children}
              </div>
            </Suspense>
          </div>
        </div>
      </LazyLoad>
    </>
  );
};

export default memo(MemberWrapper);
